import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { apiClient } from "@/lib/api";
import { useAuthStore } from "@/stores/authStore";
import type { VendingMachine, CreateOrderRequest } from "@/types";
import {
  Upload,
  FileText,
  Printer,
  MapPin,
  DollarSign,
  Settings,
  Loader2,
  CheckCircle,
} from "lucide-react";

const orderSchema = z.object({
  machineId: z.string().min(1, "Please select a machine"),
  modelFile: z.instanceof(File, "Please upload a 3D model file"),
  material: z.string().min(1, "Please select a material"),
  infill: z.number().min(5).max(100, "Infill must be between 5% and 100%"),
  layerHeight: z.number().min(0.1).max(0.5, "Layer height must be between 0.1mm and 0.5mm"),
  quantity: z.number().min(1, "Quantity must be at least 1"),
  notes: z.string().optional(),
});

type OrderFormData = z.infer<typeof orderSchema>;

interface OrderCreationDialogProps {
  trigger?: React.ReactNode;
  selectedMachineId?: string;
  onOrderCreated?: () => void;
}

export function OrderCreationDialog({
  trigger,
  selectedMachineId,
  onOrderCreated,
}: OrderCreationDialogProps) {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const [open, setOpen] = useState(false);
  const [machines, setMachines] = useState<VendingMachine[]>([]);
  const [loading, setLoading] = useState(false);
  const [machinesLoading, setMachinesLoading] = useState(false);
  const [step, setStep] = useState<"machine" | "file" | "settings" | "review">("machine");
  const [estimatedCost, setEstimatedCost] = useState<number | null>(null);

  const form = useForm<OrderFormData>({
    resolver: zodResolver(orderSchema),
    defaultValues: {
      machineId: selectedMachineId || "",
      material: "PLA",
      infill: 20,
      layerHeight: 0.2,
      quantity: 1,
      notes: "",
    },
  });

  const watchedValues = form.watch();

  // Load machines when dialog opens
  const loadMachines = async () => {
    try {
      setMachinesLoading(true);
      const response = await apiClient.getVendingMachines();
      setMachines(response.data.filter(machine => machine.is_active && !machine.maintenance_required));
    } catch (error) {
      console.error("Failed to load machines:", error);
    } finally {
      setMachinesLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen) {
      loadMachines();
      setStep("machine");
      form.reset({
        machineId: selectedMachineId || "",
        material: "PLA",
        infill: 20,
        layerHeight: 0.2,
        quantity: 1,
        notes: "",
      });
    }
  };

  const calculateEstimatedCost = () => {
    // Simple cost calculation - in real app this would be more sophisticated
    const baseCost = 5.0;
    const materialCosts = { PLA: 0.05, ABS: 0.06, PETG: 0.07, TPU: 0.08 };
    const materialCost = materialCosts[watchedValues.material as keyof typeof materialCosts] || 0.05;
    const infillMultiplier = watchedValues.infill / 100;
    const layerMultiplier = 1 / watchedValues.layerHeight;
    
    const estimated = (baseCost + (materialCost * 100 * infillMultiplier * layerMultiplier)) * watchedValues.quantity;
    setEstimatedCost(Math.round(estimated * 100) / 100);
  };

  const onSubmit = async (data: OrderFormData) => {
    if (!user?.id) return;

    try {
      setLoading(true);
      
      // Upload file first (simplified - in real app you'd upload to cloud storage)
      const formData = new FormData();
      formData.append("file", data.modelFile);
      
      // For now, we'll use a placeholder URL
      const modelFileUrl = `uploads/${data.modelFile.name}`;

      const orderData: CreateOrderRequest = {
        customer_id: user.id,
        product_id: "default-product", // This would come from a product catalog
        quantity: data.quantity,
        machine_id: data.machineId,
        cost: estimatedCost || 0,
        model_file_url: modelFileUrl,
        print_settings: {
          material: data.material,
          infill: data.infill,
          layerHeight: data.layerHeight,
          notes: data.notes,
        },
      };

      await apiClient.createOrder(orderData);
      
      setOpen(false);
      onOrderCreated?.();
    } catch (error) {
      console.error("Failed to create order:", error);
    } finally {
      setLoading(false);
    }
  };

  const selectedMachine = machines.find(m => m.id === watchedValues.machineId);

  const renderStepContent = () => {
    switch (step) {
      case "machine":
        return (
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="machineId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Select Machine</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a 3D printer" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {machinesLoading ? (
                        <div className="flex items-center justify-center p-4">
                          <Loader2 className="h-4 w-4 animate-spin" />
                        </div>
                      ) : (
                        machines.map((machine) => (
                          <SelectItem key={machine.id} value={machine.id}>
                            <div className="flex items-center gap-2">
                              <Printer className="h-4 w-4" />
                              <div>
                                <div className="font-medium">{machine.serial_number}</div>
                                <div className="text-sm text-muted-foreground">
                                  {machine.location}
                                </div>
                              </div>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {selectedMachine && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Selected Machine</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Printer className="h-4 w-4" />
                      <span className="font-medium">{selectedMachine.serial_number}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span className="text-sm">{selectedMachine.location}</span>
                    </div>
                    <div className="flex gap-1 mt-2">
                      <Badge variant="secondary">Available</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        );

      case "file":
        return (
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="modelFile"
              render={({ field: { onChange, value, ...field } }) => (
                <FormItem>
                  <FormLabel>3D Model File</FormLabel>
                  <FormControl>
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                      <Input
                        type="file"
                        accept=".stl,.obj,.3mf"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) onChange(file);
                        }}
                        className="hidden"
                        id="file-upload"
                        {...field}
                      />
                      <Label htmlFor="file-upload" className="cursor-pointer">
                        <div className="space-y-2">
                          <Upload className="h-8 w-8 mx-auto text-muted-foreground" />
                          <div className="text-sm">
                            {value ? (
                              <div className="flex items-center justify-center gap-2">
                                <FileText className="h-4 w-4" />
                                <span className="font-medium">{value.name}</span>
                              </div>
                            ) : (
                              <>
                                <div className="font-medium">Click to upload your 3D model</div>
                                <div className="text-muted-foreground">
                                  Supports STL, OBJ, 3MF files
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      </Label>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Upload your 3D model file. Maximum file size: 50MB
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        );

      case "settings":
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="material"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Material</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="PLA">PLA</SelectItem>
                        <SelectItem value="ABS">ABS</SelectItem>
                        <SelectItem value="PETG">PETG</SelectItem>
                        <SelectItem value="TPU">TPU</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantity</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="infill"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Infill (%)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="5"
                        max="100"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="layerHeight"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Layer Height (mm)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.1"
                        min="0.1"
                        max="0.5"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any special instructions or notes..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        );

      case "review":
        return (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Machine:</span>
                  <span className="font-medium">{selectedMachine?.serial_number}</span>
                </div>
                <div className="flex justify-between">
                  <span>Location:</span>
                  <span>{selectedMachine?.location}</span>
                </div>
                <div className="flex justify-between">
                  <span>File:</span>
                  <span className="font-medium">{watchedValues.modelFile?.name}</span>
                </div>
                <div className="flex justify-between">
                  <span>Material:</span>
                  <span>{watchedValues.material}</span>
                </div>
                <div className="flex justify-between">
                  <span>Quantity:</span>
                  <span>{watchedValues.quantity}</span>
                </div>
                <div className="flex justify-between">
                  <span>Infill:</span>
                  <span>{watchedValues.infill}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Layer Height:</span>
                  <span>{watchedValues.layerHeight}mm</span>
                </div>
                {estimatedCost && (
                  <div className="flex justify-between text-lg font-semibold border-t pt-2">
                    <span>Estimated Cost:</span>
                    <span className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4" />
                      {estimatedCost}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Upload className="mr-2 h-4 w-4" />
            Create Order
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Order</DialogTitle>
          <DialogDescription>
            Upload your 3D model and configure print settings
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Step indicator */}
            <div className="flex items-center justify-between">
              {["machine", "file", "settings", "review"].map((stepName, index) => (
                <div
                  key={stepName}
                  className={`flex items-center ${
                    index < 3 ? "flex-1" : ""
                  }`}
                >
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      step === stepName
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted text-muted-foreground"
                    }`}
                  >
                    {index + 1}
                  </div>
                  {index < 3 && (
                    <div className="flex-1 h-px bg-muted mx-2" />
                  )}
                </div>
              ))}
            </div>

            {renderStepContent()}

            {/* Navigation buttons */}
            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  const steps = ["machine", "file", "settings", "review"];
                  const currentIndex = steps.indexOf(step);
                  if (currentIndex > 0) {
                    setStep(steps[currentIndex - 1] as typeof step);
                  }
                }}
                disabled={step === "machine"}
              >
                Previous
              </Button>

              {step !== "review" ? (
                <Button
                  type="button"
                  onClick={() => {
                    const steps = ["machine", "file", "settings", "review"];
                    const currentIndex = steps.indexOf(step);
                    if (currentIndex < steps.length - 1) {
                      if (step === "settings") {
                        calculateEstimatedCost();
                      }
                      setStep(steps[currentIndex + 1] as typeof step);
                    }
                  }}
                  disabled={
                    (step === "machine" && !watchedValues.machineId) ||
                    (step === "file" && !watchedValues.modelFile)
                  }
                >
                  Next
                </Button>
              ) : (
                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Order...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Create Order
                    </>
                  )}
                </Button>
              )}
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
